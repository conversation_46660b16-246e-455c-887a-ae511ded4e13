import React, { useState } from 'react';

// --- SVG Icon Components ---
const UserIcon = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
    <circle cx="12" cy="7" r="4" />
  </svg>
);

const LockIcon = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
    <rect width="18" height="11" x="3" y="11" rx="2" ry="2" />
    <path d="M7 11V7a5 5 0 0 1 10 0v4" />
  </svg>
);

const ArrowRightIcon = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
    <path d="M5 12h14" />
    <path d="m12 5 7 7-7 7" />
  </svg>
);

// --- Updated SVG Logo Component ---
// Corrected the fill color for the dark chevron to match the primary text color.
const LohiaLogo = ({ className, primaryColor = '#111827', secondaryColor = '#6B7280' }) => (
    <svg
      viewBox="0 0 250 50" // Adjusted viewbox for better proportions
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      aria-label="LohiaCorp Logo"
    >
      {/* The text part of the logo */}
      <text
        x="5"
        y="35"
        fontFamily="'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif"
        fontSize="32"
        letterSpacing="-0.5"
      >
        <tspan fontWeight="bold" fill={primaryColor}>Lohia</tspan>
        <tspan fontWeight="500" fill={secondaryColor}>Corp</tspan>
      </text>
      {/* The graphic/symbol part of the logo */}
      <g transform="translate(195, 10)">
          {/* CORRECTED: The dark chevron now uses the primaryColor prop for its fill. */}
          <path d="M10 0 L25 15 L10 30 L15 30 L30 15 L15 0 Z" fill={primaryColor} />
          {/* The yellow chevron */}
          <path d="M0 0 L15 15 L0 30 L5 30 L20 15 L5 0 Z" fill="#FBBF24" />
      </g>
    </svg>
);


// Main App Component
export default function App() {
  const [userId, setUserId] = useState('');
  const [password, setPassword] = useState('');
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [error, setError] = useState('');

  // --- MOCK LOGIN FUNCTION ---
  const handleLogin = (e) => {
    e.preventDefault();
    setError('');

    if (!userId || !password) {
      setError('Please enter both User ID and Password.');
      return;
    }

    setIsLoggingIn(true);
    setTimeout(() => {
      console.log('Logging in with:', { userId, password });
      if (userId === 'admin' && password === 'password') {
        console.log('Login successful!');
      } else {
        setError('Invalid credentials. Please try again.');
      }
      setIsLoggingIn(false);
    }, 2000);
  };

  // --- JSX RENDER ---
  return (
    <div className="min-h-screen w-full bg-gray-50 flex items-center justify-center font-sans p-4">
      <div className="grid grid-cols-1 lg:grid-cols-2 max-w-4xl w-full mx-auto bg-white rounded-3xl shadow-2xl shadow-gray-300/40 overflow-hidden">
        
        {/* --- Left Side: Branding & Image --- */}
        <div className="hidden lg:flex flex-col items-center justify-center p-12 bg-gradient-to-br from-gray-800 to-gray-900 text-white relative">
           <div className="absolute top-0 left-0 w-32 h-32 bg-gray-700/50 rounded-full -translate-x-1/2 -translate-y-1/2"></div>
           <div className="absolute bottom-0 right-0 w-48 h-48 bg-yellow-400/30 rounded-full translate-x-1/2 translate-y-1/2"></div>
           <div className="z-10 text-center">
             {/* The SVG Logo is used here, with colors passed for the dark background */}
             <LohiaLogo className="w-56 h-auto mb-4" primaryColor="#FFFFFF" secondaryColor="#D1D5DB" />
             <p className="text-gray-300">Welcome back to your digital workspace.</p>
           </div>
        </div>

        {/* --- Right Side: Login Form --- */}
        <div className="p-8 md:p-12">
          {/* Logo for mobile view */}
          <div className="lg:hidden text-center mb-8">
             {/* The SVG Logo is used here with default colors (now including a darker primary) */}
             <LohiaLogo className="w-56 h-auto mx-auto" />
          </div>

          <h2 className="text-3xl font-bold text-gray-800 mb-2">Login</h2>
          <p className="text-gray-500 mb-8">Enter your credentials to access your account.</p>

          <form onSubmit={handleLogin}>
            <div className="relative mb-6">
              <UserIcon className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input type="text" placeholder="User ID" value={userId} onChange={(e) => setUserId(e.target.value)} className="w-full pl-12 pr-4 py-3 bg-gray-100 border border-transparent rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all" aria-label="User ID" />
            </div>
            <div className="relative mb-4">
              <LockIcon className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input type="password" placeholder="Password" value={password} onChange={(e) => setPassword(e.target.value)} className="w-full pl-12 pr-4 py-3 bg-gray-100 border border-transparent rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all" aria-label="Password" />
            </div>
            <div className="text-right mb-8">
                <a href="#" className="text-sm text-yellow-600 hover:text-yellow-700 hover:underline transition-colors">
                    Forgot Password?
                </a>
            </div>
            {error && (
              <div className="mb-4 p-3 bg-red-100 text-red-700 border border-red-200 rounded-lg text-sm text-center">
                {error}
              </div>
            )}
            <button type="submit" disabled={isLoggingIn} className="w-full bg-yellow-500 text-gray-900 font-bold py-3 rounded-lg hover:bg-yellow-600 focus:outline-none focus:ring-4 focus:ring-yellow-500/50 transition-all duration-300 ease-in-out flex items-center justify-center disabled:bg-gray-400 disabled:cursor-not-allowed">
              {isLoggingIn ? (
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
              ) : (
                <>
                  LOGIN
                  <ArrowRightIcon className="ml-2 w-5 h-5" />
                </>
              )}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}
